('D:\\projects\\Python\\learn01\\ai_tool\\视频处理\\build\\视频原创化处理工具\\PYZ-00.pyz',
 [('_compat_pickle',
   'D:\\environment\\python\\3.12\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'D:\\environment\\python\\3.12\\Lib\\_compression.py',
   'PYMODULE'),
  ('_py_abc', 'D:\\environment\\python\\3.12\\Lib\\_py_abc.py', 'PYMODULE'),
  ('_pydatetime',
   'D:\\environment\\python\\3.12\\Lib\\_pydatetime.py',
   'PYMODULE'),
  ('_pydecimal',
   'D:\\environment\\python\\3.12\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('_strptime', 'D:\\environment\\python\\3.12\\Lib\\_strptime.py', 'PYMODULE'),
  ('_threading_local',
   'D:\\environment\\python\\3.12\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse', 'D:\\environment\\python\\3.12\\Lib\\argparse.py', 'PYMODULE'),
  ('ast', 'D:\\environment\\python\\3.12\\Lib\\ast.py', 'PYMODULE'),
  ('base64', 'D:\\environment\\python\\3.12\\Lib\\base64.py', 'PYMODULE'),
  ('bisect', 'D:\\environment\\python\\3.12\\Lib\\bisect.py', 'PYMODULE'),
  ('bz2', 'D:\\environment\\python\\3.12\\Lib\\bz2.py', 'PYMODULE'),
  ('calendar', 'D:\\environment\\python\\3.12\\Lib\\calendar.py', 'PYMODULE'),
  ('contextlib',
   'D:\\environment\\python\\3.12\\Lib\\contextlib.py',
   'PYMODULE'),
  ('contextvars',
   'D:\\environment\\python\\3.12\\Lib\\contextvars.py',
   'PYMODULE'),
  ('copy', 'D:\\environment\\python\\3.12\\Lib\\copy.py', 'PYMODULE'),
  ('csv', 'D:\\environment\\python\\3.12\\Lib\\csv.py', 'PYMODULE'),
  ('dataclasses',
   'D:\\environment\\python\\3.12\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime', 'D:\\environment\\python\\3.12\\Lib\\datetime.py', 'PYMODULE'),
  ('decimal', 'D:\\environment\\python\\3.12\\Lib\\decimal.py', 'PYMODULE'),
  ('dis', 'D:\\environment\\python\\3.12\\Lib\\dis.py', 'PYMODULE'),
  ('email',
   'D:\\environment\\python\\3.12\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   'D:\\environment\\python\\3.12\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\environment\\python\\3.12\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'D:\\environment\\python\\3.12\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'D:\\environment\\python\\3.12\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'D:\\environment\\python\\3.12\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'D:\\environment\\python\\3.12\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'D:\\environment\\python\\3.12\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'D:\\environment\\python\\3.12\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'D:\\environment\\python\\3.12\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'D:\\environment\\python\\3.12\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'D:\\environment\\python\\3.12\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'D:\\environment\\python\\3.12\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'D:\\environment\\python\\3.12\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'D:\\environment\\python\\3.12\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'D:\\environment\\python\\3.12\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'D:\\environment\\python\\3.12\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'D:\\environment\\python\\3.12\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'D:\\environment\\python\\3.12\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'D:\\environment\\python\\3.12\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('fnmatch', 'D:\\environment\\python\\3.12\\Lib\\fnmatch.py', 'PYMODULE'),
  ('fractions', 'D:\\environment\\python\\3.12\\Lib\\fractions.py', 'PYMODULE'),
  ('getopt', 'D:\\environment\\python\\3.12\\Lib\\getopt.py', 'PYMODULE'),
  ('gettext', 'D:\\environment\\python\\3.12\\Lib\\gettext.py', 'PYMODULE'),
  ('gzip', 'D:\\environment\\python\\3.12\\Lib\\gzip.py', 'PYMODULE'),
  ('hashlib', 'D:\\environment\\python\\3.12\\Lib\\hashlib.py', 'PYMODULE'),
  ('importlib',
   'D:\\environment\\python\\3.12\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'D:\\environment\\python\\3.12\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\environment\\python\\3.12\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\environment\\python\\3.12\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'D:\\environment\\python\\3.12\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'D:\\environment\\python\\3.12\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\environment\\python\\3.12\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\environment\\python\\3.12\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\environment\\python\\3.12\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\environment\\python\\3.12\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\environment\\python\\3.12\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\environment\\python\\3.12\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\environment\\python\\3.12\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'D:\\environment\\python\\3.12\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\environment\\python\\3.12\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'D:\\environment\\python\\3.12\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'D:\\environment\\python\\3.12\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'D:\\environment\\python\\3.12\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'D:\\environment\\python\\3.12\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'D:\\environment\\python\\3.12\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'D:\\environment\\python\\3.12\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util',
   'D:\\environment\\python\\3.12\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect', 'D:\\environment\\python\\3.12\\Lib\\inspect.py', 'PYMODULE'),
  ('ipaddress', 'D:\\environment\\python\\3.12\\Lib\\ipaddress.py', 'PYMODULE'),
  ('json', 'D:\\environment\\python\\3.12\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.decoder',
   'D:\\environment\\python\\3.12\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'D:\\environment\\python\\3.12\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'D:\\environment\\python\\3.12\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('logging',
   'D:\\environment\\python\\3.12\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('lzma', 'D:\\environment\\python\\3.12\\Lib\\lzma.py', 'PYMODULE'),
  ('numbers', 'D:\\environment\\python\\3.12\\Lib\\numbers.py', 'PYMODULE'),
  ('opcode', 'D:\\environment\\python\\3.12\\Lib\\opcode.py', 'PYMODULE'),
  ('pathlib', 'D:\\environment\\python\\3.12\\Lib\\pathlib.py', 'PYMODULE'),
  ('pickle', 'D:\\environment\\python\\3.12\\Lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'D:\\environment\\python\\3.12\\Lib\\pprint.py', 'PYMODULE'),
  ('py_compile',
   'D:\\environment\\python\\3.12\\Lib\\py_compile.py',
   'PYMODULE'),
  ('quopri', 'D:\\environment\\python\\3.12\\Lib\\quopri.py', 'PYMODULE'),
  ('random', 'D:\\environment\\python\\3.12\\Lib\\random.py', 'PYMODULE'),
  ('selectors', 'D:\\environment\\python\\3.12\\Lib\\selectors.py', 'PYMODULE'),
  ('shutil', 'D:\\environment\\python\\3.12\\Lib\\shutil.py', 'PYMODULE'),
  ('signal', 'D:\\environment\\python\\3.12\\Lib\\signal.py', 'PYMODULE'),
  ('socket', 'D:\\environment\\python\\3.12\\Lib\\socket.py', 'PYMODULE'),
  ('statistics',
   'D:\\environment\\python\\3.12\\Lib\\statistics.py',
   'PYMODULE'),
  ('string', 'D:\\environment\\python\\3.12\\Lib\\string.py', 'PYMODULE'),
  ('stringprep',
   'D:\\environment\\python\\3.12\\Lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'D:\\environment\\python\\3.12\\Lib\\subprocess.py',
   'PYMODULE'),
  ('tarfile', 'D:\\environment\\python\\3.12\\Lib\\tarfile.py', 'PYMODULE'),
  ('tempfile', 'D:\\environment\\python\\3.12\\Lib\\tempfile.py', 'PYMODULE'),
  ('textwrap', 'D:\\environment\\python\\3.12\\Lib\\textwrap.py', 'PYMODULE'),
  ('threading', 'D:\\environment\\python\\3.12\\Lib\\threading.py', 'PYMODULE'),
  ('tkinter',
   'D:\\environment\\python\\3.12\\Lib\\tkinter\\__init__.py',
   'PYMODULE'),
  ('tkinter.commondialog',
   'D:\\environment\\python\\3.12\\Lib\\tkinter\\commondialog.py',
   'PYMODULE'),
  ('tkinter.constants',
   'D:\\environment\\python\\3.12\\Lib\\tkinter\\constants.py',
   'PYMODULE'),
  ('tkinter.dialog',
   'D:\\environment\\python\\3.12\\Lib\\tkinter\\dialog.py',
   'PYMODULE'),
  ('tkinter.filedialog',
   'D:\\environment\\python\\3.12\\Lib\\tkinter\\filedialog.py',
   'PYMODULE'),
  ('tkinter.messagebox',
   'D:\\environment\\python\\3.12\\Lib\\tkinter\\messagebox.py',
   'PYMODULE'),
  ('tkinter.simpledialog',
   'D:\\environment\\python\\3.12\\Lib\\tkinter\\simpledialog.py',
   'PYMODULE'),
  ('tkinter.ttk',
   'D:\\environment\\python\\3.12\\Lib\\tkinter\\ttk.py',
   'PYMODULE'),
  ('token', 'D:\\environment\\python\\3.12\\Lib\\token.py', 'PYMODULE'),
  ('tokenize', 'D:\\environment\\python\\3.12\\Lib\\tokenize.py', 'PYMODULE'),
  ('tracemalloc',
   'D:\\environment\\python\\3.12\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('typing', 'D:\\environment\\python\\3.12\\Lib\\typing.py', 'PYMODULE'),
  ('urllib',
   'D:\\environment\\python\\3.12\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.parse',
   'D:\\environment\\python\\3.12\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('zipfile',
   'D:\\environment\\python\\3.12\\Lib\\zipfile\\__init__.py',
   'PYMODULE'),
  ('zipfile._path',
   'D:\\environment\\python\\3.12\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE'),
  ('zipfile._path.glob',
   'D:\\environment\\python\\3.12\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE')])
