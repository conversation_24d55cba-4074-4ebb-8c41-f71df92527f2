#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建应用程序图标的辅助脚本
"""

import os
import sys

try:
    from PIL import Image, ImageDraw, ImageFont
except ImportError:
    print("需要安装Pillow库来创建图标")
    print("请运行: pip install Pillow")
    sys.exit(1)


def create_video_icon():
    """创建一个简单的视频处理图标"""
    # 创建一个256x256的图像
    size = 256
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # 背景 - 渐变蓝色
    for i in range(size):
        color = int(50 + (i / size) * 150)
        draw.rectangle([(0, i), (size, i+1)], fill=(0, color, 200, 255))
    
    # 绘制视频播放按钮图标
    # 外圆
    center = size // 2
    radius = size // 3
    draw.ellipse(
        [(center - radius, center - radius), (center + radius, center + radius)],
        fill=(255, 255, 255, 255),
        outline=(0, 0, 0, 255),
        width=3
    )
    
    # 播放三角形
    triangle_size = radius // 2
    triangle = [
        (center - triangle_size // 2, center - triangle_size),
        (center - triangle_size // 2, center + triangle_size),
        (center + triangle_size, center)
    ]
    draw.polygon(triangle, fill=(0, 150, 200, 255))
    
    # 添加齿轮表示处理
    gear_center_x = center + radius // 2
    gear_center_y = center - radius // 2
    gear_radius = 30
    
    # 绘制齿轮
    draw.ellipse(
        [(gear_center_x - gear_radius, gear_center_y - gear_radius),
         (gear_center_x + gear_radius, gear_center_y + gear_radius)],
        fill=(255, 200, 0, 255),
        outline=(0, 0, 0, 255),
        width=2
    )
    
    # 齿轮中心
    draw.ellipse(
        [(gear_center_x - 10, gear_center_y - 10),
         (gear_center_x + 10, gear_center_y + 10)],
        fill=(255, 255, 255, 255)
    )
    
    # 添加文字
    try:
        # 尝试使用系统字体
        font = ImageFont.truetype("arial.ttf", 20)
    except:
        # 使用默认字体
        font = ImageFont.load_default()
    
    text = "VIDEO"
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_x = (size - text_width) // 2
    draw.text((text_x, size - 40), text, fill=(255, 255, 255, 255), font=font)
    
    # 保存为多种尺寸的ICO文件
    img.save('icon.png')
    print("✓ 已创建 icon.png")
    
    # 创建ICO文件（包含多种尺寸）
    icon_sizes = [(16, 16), (32, 32), (48, 48), (64, 64), (128, 128), (256, 256)]
    icons = []
    
    for icon_size in icon_sizes:
        resized = img.resize(icon_size, Image.Resampling.LANCZOS)
        icons.append(resized)
    
    # 保存ICO文件
    icons[5].save('icon.ico', format='ICO', sizes=icon_sizes)
    print("✓ 已创建 icon.ico")
    
    return True


def main():
    """主函数"""
    print("=" * 50)
    print("视频原创化处理工具 - 图标生成器")
    print("=" * 50)
    
    if os.path.exists('icon.ico'):
        print("\n图标文件已存在！")
        if input("是否覆盖现有图标? (y/n): ").lower() != 'y':
            return
    
    print("\n正在生成图标...")
    
    try:
        if create_video_icon():
            print("\n✓ 图标创建成功！")
            print("- icon.ico: Windows图标文件")
            print("- icon.png: PNG格式图标")
        else:
            print("\n✗ 图标创建失败")
    except Exception as e:
        print(f"\n✗ 错误: {e}")
        print("\n请确保已安装Pillow库：")
        print("pip install Pillow")


if __name__ == "__main__":
    main()
