# 视频原创化处理工具 - 打包说明

## 快速开始

最简单的方法是运行 **`一键打包.bat`**，它会自动完成所有步骤。

## 文件说明

### 核心文件
- `01.py` - 主程序源代码
- `icon.ico` - 应用程序图标（256x256）
- `icon.png` - PNG格式图标

### 打包相关
- `一键打包.bat` - 自动化打包脚本（推荐使用）
- `build.py` - PyInstaller打包配置脚本
- `打包程序.bat` - 简单打包脚本
- `create_icon.py` - 图标生成工具
- `生成图标.bat` - 图标生成批处理

### 依赖相关
- `requirements.txt` - Python依赖列表
- `安装依赖.bat` - 依赖安装脚本

## 详细步骤

### 1. 准备图标（如果没有）

```bash
# 方法一：运行批处理
生成图标.bat

# 方法二：运行Python脚本
python create_icon.py
```

### 2. 安装依赖

```bash
# 方法一：运行批处理
安装依赖.bat

# 方法二：手动安装
pip install -r requirements.txt
```

### 3. 执行打包

```bash
# 方法一：一键打包（推荐）
一键打包.bat

# 方法二：运行打包脚本
python build.py

# 方法三：手动打包
pyinstaller --onefile --windowed --icon=icon.ico --name=视频原创化处理工具 01.py
```

## 打包选项说明

- `--onefile` - 打包成单个exe文件
- `--windowed` - 不显示控制台窗口
- `--icon=icon.ico` - 使用自定义图标
- `--name` - 指定输出文件名

## 输出文件

打包成功后，会在 `dist` 目录下生成：
- `视频原创化处理工具.exe` - 可执行文件（包含图标）

## 分发说明

分发时只需要提供：
1. `视频原创化处理工具.exe` - 主程序
2. `README.txt` - 使用说明

用户需要自行安装FFmpeg。

## 常见问题

### Q: 提示缺少模块？
A: 运行 `pip install -r requirements.txt` 安装依赖

### Q: 图标没有显示？
A: 确保 `icon.ico` 文件存在且格式正确

### Q: 打包后文件很大？
A: 这是正常的，PyInstaller会包含Python解释器和所有依赖

### Q: 杀毒软件报警？
A: 这是PyInstaller打包的常见问题，可以：
- 将exe文件加入白名单
- 使用代码签名证书签名

## 优化建议

1. **减小文件大小**
   - 使用UPX压缩（PyInstaller自带）
   - 排除不必要的模块

2. **提高启动速度**
   - 使用 `--onedir` 而非 `--onefile`
   - 优化导入语句

3. **改善兼容性**
   - 在目标系统上测试
   - 注意Python版本兼容性

## 技术支持

如遇问题，请检查：
1. Python版本（建议3.7+）
2. PyInstaller版本（建议5.0+）
3. 系统环境变量
4. 防病毒软件设置
