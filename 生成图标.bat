@echo off
chcp 65001 >nul
echo ========================================
echo 视频原创化处理工具 - 图标生成器
echo ========================================
echo.

if exist icon.ico (
    echo 检测到已存在 icon.ico 文件
    echo.
    choice /C YN /M "是否重新生成图标"
    if errorlevel 2 goto end
)

echo 正在生成图标...
python create_icon.py

if errorlevel 1 (
    echo.
    echo ========================================
    echo 图标生成失败！
    echo ========================================
    echo 可能的原因：
    echo 1. 未安装Python
    echo 2. 未安装Pillow库
    echo.
    echo 解决方法：
    echo 运行"安装依赖.bat"或手动执行：
    echo pip install Pillow
    echo ========================================
) else (
    echo.
    echo ========================================
    echo 图标生成成功！
    echo ========================================
    echo 已创建文件：
    echo - icon.ico （Windows图标）
    echo - icon.png （PNG格式）
    echo ========================================
)

:end
echo.
echo 按任意键退出...
pause >nul
