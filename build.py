#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频原创化处理工具 - PyInstaller打包脚本
自动处理打包配置和依赖
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path


def check_pyinstaller():
    """检查PyInstaller是否已安装"""
    try:
        import PyInstaller
        print(f"✓ PyInstaller已安装 (版本: {PyInstaller.__version__})")
        return True
    except ImportError:
        print("✗ PyInstaller未安装")
        return False


def install_pyinstaller():
    """安装PyInstaller"""
    print("正在安装PyInstaller...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✓ PyInstaller安装成功")
        return True
    except subprocess.CalledProcessError:
        print("✗ PyInstaller安装失败")
        return False


def create_spec_file():
    """创建PyInstaller spec文件"""
    # 检查图标文件是否存在
    icon_path = 'icon.ico' if os.path.exists('icon.ico') else None
    icon_line = f"icon='{icon_path}'," if icon_path else "icon=None,"
    
    spec_content = """# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['01.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=['tkinter', 'pathlib', 'tempfile', 'json'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='视频原创化处理工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 不显示控制台窗口
    disable_windowed_traceback=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    """ + icon_line + """
)
"""
    
    with open('video_tool.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    print("✓ spec文件创建成功")
    if icon_path:
        print(f"✓ 配置使用图标: {icon_path}")


def create_icon_if_not_exists():
    """如果图标不存在，创建一个简单的图标"""
    if not os.path.exists('icon.ico'):
        print("⚠ 未找到icon.ico文件")
        # 这里可以添加创建默认图标的代码
        return False
    else:
        print("✓ 找到图标文件: icon.ico")
        return True


def build_exe():
    """构建exe文件"""
    print("\n开始构建exe文件...")
    
    # 检查图标文件
    has_icon = create_icon_if_not_exists()
    
    # 清理旧的构建文件
    for folder in ['build', 'dist']:
        if os.path.exists(folder):
            shutil.rmtree(folder)
            print(f"✓ 清理{folder}目录")
    
    # 构建PyInstaller命令
    pyinstaller_args = [
        sys.executable, "-m", "PyInstaller",
        "--onefile",  # 打包成单个文件
        "--windowed",  # Windows下不显示控制台
        "--name", "视频原创化处理工具",
        "--clean",  # 清理临时文件
    ]
    
    # 如果有图标文件，添加图标参数
    if has_icon:
        pyinstaller_args.extend(["--icon", "icon.ico"])
        print("✓ 将使用icon.ico作为程序图标")
    
    # 添加主程序文件
    pyinstaller_args.append("01.py")
    
    # 运行PyInstaller
    try:
        subprocess.check_call(pyinstaller_args)
        print("\n✓ exe文件构建成功!")
        print(f"输出文件位置: {os.path.abspath('dist/视频原创化处理工具.exe')}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"\n✗ 构建失败: {e}")
        return False


def create_readme():
    """创建使用说明"""
    readme_content = """# 视频原创化处理工具 使用说明

## 程序信息
- 版本：2.0
- 图标：内置应用程序图标
- 要求：Windows 7及以上系统

## 功能特点

### 基础功能
- **镜像翻转**: 水平翻转视频画面
- **智能裁剪**: 支持16:9、9:16、1:1、4:3等比例
- **变速处理**: 支持0.8x-1.2x变速或随机变速
- **滤镜效果**: 随机调整亮度、对比度、饱和度
- **添加水印**: 支持静态/动态水印
- **音频处理**: 音量调整、音调变化、高通滤波

### 高级功能
- **画面抖动**: 随机轻微抖动效果
- **添加噪点**: 为视频添加噪点
- **随机边框**: 添加不同颜色和宽度的边框
- **微旋转**: 轻微旋转画面角度
- **自动剪切**: 自动剪除开头结尾部分
- **动态水印**: 水印位置随时间移动

### 其他功能
- **批量处理**: 同时处理多个视频文件
- **配置保存/加载**: 保存常用的处理设置
- **预览功能**: 快速预览处理效果（前10秒）

## 使用方法

1. **准备工作**
   - 确保系统已安装FFmpeg
   - Windows: 下载FFmpeg并添加到系统PATH
   - macOS: `brew install ffmpeg`
   - Linux: `sudo apt-get install ffmpeg`

2. **基本使用**
   - 运行程序：双击 `视频原创化处理工具.exe`
   - 选择源视频文件
   - 选择输出目录
   - 勾选需要的处理选项
   - 设置处理强度（低/中/高）
   - 点击"开始处理"

3. **批量处理**
   - 点击"批量处理"按钮
   - 选择多个视频文件
   - 选择输出目录
   - 程序会使用当前设置处理所有文件

4. **配置管理**
   - 调整好处理选项后，点击"保存配置"
   - 需要时点击"加载配置"恢复设置

## 注意事项

1. 处理大文件可能需要较长时间，请耐心等待
2. 确保输出目录有足够的存储空间
3. 处理过程中请勿关闭程序
4. 如果路径包含中文或特殊字符，程序会自动处理

## 处理强度说明

- **低**: 轻微调整，变化不明显
- **中**: 适度调整，推荐使用
- **高**: 较大调整，可能影响观看体验

## 常见问题

**Q: 提示FFmpeg未安装？**
A: 请按照准备工作中的说明安装FFmpeg

**Q: 处理失败？**
A: 检查日志信息，可能是文件格式不支持或路径问题

**Q: 处理后的视频质量下降？**
A: 尝试降低处理强度或减少处理选项

## 技术支持

如遇到问题，请查看程序日志或联系技术支持。
"""
    
    with open('README.txt', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    print("✓ 使用说明创建成功")


def create_default_icon():
    """创建默认图标的说明"""
    print("\n" + "=" * 50)
    print("图标文件说明：")
    print("=" * 50)
    print("程序需要一个 icon.ico 文件作为应用程序图标")
    print("\n创建图标的方法：")
    print("1. 使用在线工具转换图片为ico格式")
    print("   推荐网站: https://convertio.co/zh/png-ico/")
    print("2. 使用图像编辑软件（如GIMP、Photoshop）")
    print("3. 使用Python PIL库生成")
    print("\n图标要求：")
    print("- 格式: ICO")
    print("- 推荐尺寸: 256x256像素")
    print("- 文件名: icon.ico")
    print("=" * 50)


def main():
    """主函数"""
    print("=" * 50)
    print("视频原创化处理工具 - 打包脚本")
    print("=" * 50)
    
    # 检查当前目录
    if not os.path.exists('01.py'):
        print("✗ 错误：未找到01.py文件")
        print("请确保在正确的目录下运行此脚本")
        return
    
    # 检查图标文件
    if not os.path.exists('icon.ico'):
        print("\n⚠ 警告：未找到icon.ico文件")
        create_default_icon()
        if input("\n是否继续打包（将使用默认图标）? (y/n): ").lower() != 'y':
            return
    
    # 检查并安装PyInstaller
    if not check_pyinstaller():
        if input("\n是否安装PyInstaller? (y/n): ").lower() == 'y':
            if not install_pyinstaller():
                return
        else:
            print("打包需要PyInstaller，请手动安装后重试")
            return
    
    # 创建使用说明
    create_readme()
    
    # 构建exe
    if build_exe():
        print("\n" + "=" * 50)
        print("✓ 打包完成！")
        print(f"✓ 可执行文件: dist/视频原创化处理工具.exe")
        print(f"✓ 使用说明: README.txt")
        print("=" * 50)
        
        # 提示是否打开输出目录
        if input("\n是否打开输出目录? (y/n): ").lower() == 'y':
            os.startfile('dist')
    else:
        print("\n✗ 打包失败，请检查错误信息")


if __name__ == "__main__":
    main()
