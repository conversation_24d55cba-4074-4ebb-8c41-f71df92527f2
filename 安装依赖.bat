@echo off
chcp 65001 >nul
echo ========================================
echo 视频原创化处理工具 - 安装依赖
echo ========================================
echo.

echo 正在安装Python依赖包...
python -m pip install -r requirements.txt

echo.
echo ========================================
echo 重要提示：
echo ========================================
echo 1. 请确保已安装FFmpeg并添加到系统PATH
echo    下载地址: https://ffmpeg.org/download.html
echo.
echo 2. Windows用户安装FFmpeg步骤：
echo    - 下载Windows版本的FFmpeg
echo    - 解压到C:\ffmpeg
echo    - 将C:\ffmpeg\bin添加到系统PATH
echo.
echo 3. 验证FFmpeg安装：
echo    在命令行运行: ffmpeg -version
echo ========================================

echo.
echo 按任意键退出...
pause >nul
