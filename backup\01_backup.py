#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频原创化处理工具
帮助创作者快速处理视频，规避抄袭风险
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import threading
from pathlib import Path
import subprocess
import tempfile
import shutil
from datetime import datetime


class VideoOriginalityTool:
    def __init__(self, root):
        self.root = root
        self.root.title("视频原创化处理工具")
        self.root.geometry("900x700")

        # 检查FFmpeg
        if not self.check_ffmpeg_installation():
            messagebox.showwarning(
                "依赖检查",
                "未检测到FFmpeg，请确保已正确安装FFmpeg并添加到系统PATH中。\n\n"
                "安装方法：\n"
                "Windows: 下载FFmpeg并添加到PATH\n"
                "macOS: brew install ffmpeg\n"
                "Linux: sudo apt-get install ffmpeg"
            )

        self.setup_ui()
        self.processing = False

    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 文件选择区域
        file_frame = ttk.LabelFrame(main_frame, text="文件选择", padding="10")
        file_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Label(file_frame, text="源视频:").grid(row=0, column=0, sticky=tk.W)
        self.source_var = tk.StringVar()
        ttk.Entry(file_frame, textvariable=self.source_var, width=50).grid(row=0, column=1, padx=(5, 5))
        ttk.Button(file_frame, text="浏览", command=self.select_source_file).grid(row=0, column=2)

        ttk.Label(file_frame, text="输出目录:").grid(row=1, column=0, sticky=tk.W)
        self.output_var = tk.StringVar()
        ttk.Entry(file_frame, textvariable=self.output_var, width=50).grid(row=1, column=1, padx=(5, 5))
        ttk.Button(file_frame, text="浏览", command=self.select_output_dir).grid(row=1, column=2)

        # 处理选项区域
        options_frame = ttk.LabelFrame(main_frame, text="原创化处理选项", padding="10")
        options_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        # 视频处理选项
        self.mirror_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="镜像翻转", variable=self.mirror_var).grid(row=0, column=0, sticky=tk.W)

        self.crop_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="智能裁剪", variable=self.crop_var).grid(row=0, column=1, sticky=tk.W)

        self.speed_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="变速处理", variable=self.speed_var).grid(row=0, column=2, sticky=tk.W)

        self.filter_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="滤镜效果", variable=self.filter_var).grid(row=1, column=0, sticky=tk.W)

        self.watermark_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="添加水印", variable=self.watermark_var).grid(row=1, column=1, sticky=tk.W)

        self.audio_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="音频处理", variable=self.audio_var).grid(row=1, column=2, sticky=tk.W)

        # 高级选项
        advanced_frame = ttk.LabelFrame(main_frame, text="高级选项", padding="10")
        advanced_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Label(advanced_frame, text="裁剪比例:").grid(row=0, column=0, sticky=tk.W)
        self.crop_ratio = tk.StringVar(value="16:9")
        crop_combo = ttk.Combobox(advanced_frame, textvariable=self.crop_ratio,
                                  values=["16:9", "9:16", "1:1", "4:3"], width=10)
        crop_combo.grid(row=0, column=1, padx=(5, 20))

        ttk.Label(advanced_frame, text="变速倍率:").grid(row=0, column=2, sticky=tk.W)
        self.speed_ratio = tk.StringVar(value="1.1")
        speed_combo = ttk.Combobox(advanced_frame, textvariable=self.speed_ratio,
                                   values=["0.9", "1.1", "1.2", "0.8"], width=10)
        speed_combo.grid(row=0, column=3, padx=(5, 0))

        # 控制按钮
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=3, column=0, columnspan=2, pady=(10, 0))

        self.process_btn = ttk.Button(control_frame, text="开始处理",
                                      command=self.start_processing)
        self.process_btn.pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(control_frame, text="预览效果",
                   command=self.preview_effect).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(control_frame, text="批量处理",
                   command=self.batch_process).pack(side=tk.LEFT)

        # 进度显示
        progress_frame = ttk.LabelFrame(main_frame, text="处理进度", padding="10")
        progress_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))

        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var,
                                            maximum=100, length=400)
        self.progress_bar.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 5))

        self.status_var = tk.StringVar(value="就绪")
        ttk.Label(progress_frame, textvariable=self.status_var).grid(row=1, column=0, sticky=tk.W)

        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="处理日志", padding="10")
        log_frame.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))

        self.log_text = tk.Text(log_frame, height=8, width=80)
        scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)

        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(5, weight=1)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)

    def select_source_file(self):
        """选择源视频文件"""
        filename = filedialog.askopenfilename(
            title="选择视频文件",
            filetypes=[
                ("视频文件", "*.mp4 *.avi *.mov *.mkv *.flv *.wmv"),
                ("所有文件", "*.*")
            ]
        )
        if filename:
            self.source_var.set(filename)

    def select_output_dir(self):
        """选择输出目录"""
        dirname = filedialog.askdirectory(title="选择输出目录")
        if dirname:
            self.output_var.set(dirname)

    def log_message(self, message):
        """添加日志消息 - 处理编码问题"""
        try:
            timestamp = datetime.now().strftime("%H:%M:%S")
            # 确保消息是字符串并处理可能的编码问题
            if isinstance(message, bytes):
                message = message.decode('utf-8', errors='ignore')

            log_entry = f"[{timestamp}] {message}\n"
            self.log_text.insert(tk.END, log_entry)
            self.log_text.see(tk.END)
            self.root.update()
        except Exception as e:
            # 如果日志记录失败，至少更新状态
            print(f"日志记录错误: {e}")

    def check_ffmpeg_installation(self):
        """检查FFmpeg是否已安装"""
        try:
            result = subprocess.run(
                ['ffmpeg', '-version'],
                capture_output=True,
                text=True,
                encoding='utf-8',
                errors='ignore',
                timeout=5
            )
            return result.returncode == 0
        except:
            return False

    def safe_file_path(self, file_path):
        """安全处理文件路径，避免编码问题"""
        try:
            # 转换为Path对象处理
            path = Path(file_path)

            # 检查路径是否包含非ASCII字符
            try:
                path.as_posix().encode('ascii')
                return str(path)
            except UnicodeEncodeError:
                # 如果包含非ASCII字符，创建临时文件
                temp_dir = tempfile.gettempdir()
                temp_name = f"temp_video_{datetime.now().strftime('%Y%m%d_%H%M%S')}{path.suffix}"
                temp_path = os.path.join(temp_dir, temp_name)

                # 复制文件到临时位置
                shutil.copy2(file_path, temp_path)
                self.log_message(f"文件路径包含特殊字符，已复制到临时位置: {temp_path}")
                return temp_path

        except Exception as e:
            self.log_message(f"路径处理错误: {e}")
            return file_path

    def start_processing(self):
        """开始处理视频 - 增加路径检查"""
        if self.processing:
            return

        source_file = self.source_var.get()
        output_dir = self.output_var.get()

        if not source_file or not output_dir:
            messagebox.showerror("错误", "请选择源文件和输出目录")
            return

        if not os.path.exists(source_file):
            messagebox.showerror("错误", "源文件不存在")
            return

        # 检查FFmpeg
        if not self.check_ffmpeg_installation():
            messagebox.showerror("错误", "FFmpeg未安装或未添加到PATH中")
            return

        self.processing = True
        self.process_btn.config(state='disabled')

        # 处理文件路径
        safe_source = self.safe_file_path(source_file)

        # 在新线程中处理
        thread = threading.Thread(target=self.process_video,
                                  args=(safe_source, output_dir))
        thread.daemon = True
        thread.start()

    def process_video(self, source_file, output_dir):
        """处理视频的主要逻辑"""
        try:
            self.log_message("开始处理视频...")
            self.status_var.set("正在处理...")

            # 生成输出文件名
            base_name = Path(source_file).stem
            output_file = os.path.join(output_dir, f"{base_name}_原创化.mp4")

            # 构建FFmpeg命令
            cmd = self.build_ffmpeg_command(source_file, output_file)

            self.log_message(f"执行命令: {' '.join(cmd[:5])}...")  # 只显示前几个参数

            # 执行处理 - 修复编码问题
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8',  # 明确指定UTF-8编码
                errors='ignore'  # 忽略编码错误
            )

            # 监控进度
            total_frames = self.get_video_frame_count(source_file)
            current_frame = 0

            while True:
                output = process.stderr.readline()
                if output == '' and process.poll() is not None:
                    break
                if output:
                    # 解析FFmpeg输出获取进度
                    frame_num = self.parse_ffmpeg_progress(output, total_frames)
                    if frame_num > current_frame:
                        current_frame = frame_num

            if process.returncode == 0:
                self.log_message(f"处理完成: {output_file}")
                self.status_var.set("处理完成")
                messagebox.showinfo("成功", f"视频处理完成!\n输出文件: {output_file}")
            else:
                error_output = process.stderr.read()
                self.log_message(f"处理失败: {error_output[:200]}...")
                self.status_var.set("处理失败")
                messagebox.showerror("错误", "视频处理失败，请检查日志")

        except UnicodeDecodeError as e:
            self.log_message(f"编码错误: {str(e)}")
            self.status_var.set("编码错误")
            messagebox.showerror("编码错误", "处理过程中遇到编码问题，请检查文件路径是否包含特殊字符")
        except Exception as e:
            self.log_message(f"错误: {str(e)}")
            self.status_var.set("处理失败")
            messagebox.showerror("错误", f"处理过程中出现错误: {str(e)}")
        finally:
            self.processing = False
            self.process_btn.config(state='normal')
            self.progress_var.set(0)

    def get_video_frame_count(self, video_path):
        """获取视频总帧数"""
        try:
            cmd = [
                'ffprobe', '-v', 'quiet', '-select_streams', 'v:0',
                '-count_frames', '-show_entries', 'stream=nb_frames',
                '-csv=p=0', video_path
            ]
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                encoding='utf-8',
                errors='ignore'
            )
            if result.returncode == 0 and result.stdout.strip().isdigit():
                return int(result.stdout.strip())
        except:
            pass
        return 1000  # 默认值

    def parse_ffmpeg_progress(self, output, total_frames=1000):
        """解析FFmpeg输出获取进度"""
        try:
            # 查找frame信息
            if 'frame=' in output:
                parts = output.split()
                for i, part in enumerate(parts):
                    if part.startswith('frame='):
                        frame_str = part.split('=')[1]
                        if frame_str.isdigit():
                            current_frame = int(frame_str)
                            progress = min((current_frame / total_frames) * 100, 95)
                            self.progress_var.set(progress)
                            return current_frame

            # 查找time信息作为备选
            if 'time=' in output:
                self.progress_var.set(min(self.progress_var.get() + 2, 95))

        except Exception as e:
            # 静默处理解析错误
            pass
        return 0

    def build_ffmpeg_command(self, input_file, output_file):
        """构建FFmpeg处理命令"""
        # 确保路径使用正斜杠，避免Windows路径问题
        input_file = input_file.replace('\\', '/')
        output_file = output_file.replace('\\', '/')

        cmd = ['ffmpeg', '-i', input_file]

        # 视频滤镜列表
        filters = []

        # 镜像翻转
        if self.mirror_var.get():
            filters.append('hflip')

        # 智能裁剪
        if self.crop_var.get():
            ratio = self.crop_ratio.get()
            if ratio == "16:9":
                filters.append('crop=ih*16/9:ih')
            elif ratio == "9:16":
                filters.append('crop=iw:iw*16/9')
            elif ratio == "1:1":
                filters.append('crop=min(iw\\,ih):min(iw\\,ih)')

        # 滤镜效果
        if self.filter_var.get():
            # 添加轻微的色彩调整
            filters.append('eq=brightness=0.05:contrast=1.1:saturation=1.1')

        # 添加水印 - 简化水印文本避免编码问题
        if self.watermark_var.get():
            watermark_text = f"Original_{datetime.now().strftime('%Y%m%d')}"
            filters.append(f'drawtext=text={watermark_text}:fontsize=24:fontcolor=white@0.5:x=w-tw-10:y=h-th-10')

        # 应用滤镜
        if filters:
            cmd.extend(['-vf', ','.join(filters)])

        # 变速处理
        if self.speed_var.get():
            speed = float(self.speed_ratio.get())
            cmd.extend(['-filter:v', f'setpts={1 / speed}*PTS'])
            if self.audio_var.get():
                cmd.extend(['-filter:a', f'atempo={speed}'])

        # 音频处理
        if self.audio_var.get() and not self.speed_var.get():
            # 添加轻微的音频效果
            cmd.extend(['-af', 'volume=1.05,highpass=f=50'])

        # 输出设置
        cmd.extend(['-c:v', 'libx264', '-crf', '23', '-preset', 'medium'])
        cmd.extend(['-c:a', 'aac', '-b:a', '128k'])
        cmd.extend(['-y', output_file])  # -y 覆盖输出文件

        return cmd

    def preview_effect(self):
        """预览处理效果"""
        source_file = self.source_var.get()
        if not source_file:
            messagebox.showerror("错误", "请先选择源文件")
            return

        self.log_message("生成预览中...")
        # 这里可以实现预览功能
        messagebox.showinfo("预览", "预览功能开发中...")

    def batch_process(self):
        """批量处理"""
        files = filedialog.askopenfilenames(
            title="选择多个视频文件",
            filetypes=[
                ("视频文件", "*.mp4 *.avi *.mov *.mkv *.flv *.wmv"),
                ("所有文件", "*.*")
            ]
        )

        if not files:
            return

        output_dir = filedialog.askdirectory(title="选择输出目录")
        if not output_dir:
            return

        self.log_message(f"开始批量处理 {len(files)} 个文件...")

        # 在新线程中批量处理
        thread = threading.Thread(target=self.batch_process_files,
                                  args=(files, output_dir))
        thread.daemon = True
        thread.start()

    def batch_process_files(self, files, output_dir):
        """批量处理文件"""
        self.processing = True
        self.process_btn.config(state='disabled')

        try:
            total_files = len(files)
            for i, file_path in enumerate(files):
                self.log_message(f"处理文件 {i + 1}/{total_files}: {Path(file_path).name}")
                self.status_var.set(f"批量处理中 ({i + 1}/{total_files})")

                # 处理单个文件
                safe_source = self.safe_file_path(file_path)
                base_name = Path(safe_source).stem
                output_file = os.path.join(output_dir, f"{base_name}_原创化.mp4")

                cmd = self.build_ffmpeg_command(safe_source, output_file)

                process = subprocess.run(
                    cmd,
                    capture_output=True,
                    text=True,
                    encoding='utf-8',
                    errors='ignore'
                )

                if process.returncode == 0:
                    self.log_message(f"✅ 完成: {Path(output_file).name}")
                else:
                    self.log_message(f"❌ 失败: {Path(file_path).name}")

                # 更新总进度
                progress = ((i + 1) / total_files) * 100
                self.progress_var.set(progress)

            self.log_message(f"批量处理完成！共处理 {total_files} 个文件")
            self.status_var.set("批量处理完成")
            messagebox.showinfo("完成", f"批量处理完成！\n共处理 {total_files} 个文件")

        except Exception as e:
            self.log_message(f"批量处理错误: {str(e)}")
            messagebox.showerror("错误", f"批量处理失败: {str(e)}")
        finally:
            self.processing = False
            self.process_btn.config(state='normal')
            self.progress_var.set(0)


def main():
    """主函数"""
    root = tk.Tk()
    app = VideoOriginalityTool(root)
    root.mainloop()


if __name__ == "__main__":
    main()