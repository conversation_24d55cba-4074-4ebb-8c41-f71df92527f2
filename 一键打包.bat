@echo off
chcp 65001 >nul
echo ========================================
echo 视频原创化处理工具 - 一键打包
echo ========================================
echo.

echo [1/4] 检查图标文件...
if not exist icon.ico (
    echo 未找到图标文件，正在生成...
    python create_icon.py
    if errorlevel 1 (
        echo 图标生成失败，将使用默认图标继续
        echo.
    ) else (
        echo ✓ 图标生成成功
    )
) else (
    echo ✓ 找到图标文件
)

echo.
echo [2/4] 检查依赖...
python -c "import PyInstaller" 2>nul
if errorlevel 1 (
    echo 正在安装打包依赖...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ✗ 依赖安装失败
        goto error
    )
    echo ✓ 依赖安装成功
) else (
    echo ✓ PyInstaller已安装
)

echo.
echo [3/4] 清理旧文件...
if exist dist rmdir /s /q dist 2>nul
if exist build rmdir /s /q build 2>nul
echo ✓ 清理完成

echo.
echo [4/4] 开始打包...
echo ========================================
python build.py

if exist "dist\视频原创化处理工具.exe" (
    echo.
    echo ========================================
    echo ✓ 打包成功！
    echo ========================================
    echo 输出文件：dist\视频原创化处理工具.exe
    echo.
    choice /C YN /M "是否打开输出目录"
    if errorlevel 1 if not errorlevel 2 start dist
) else (
    goto error
)

goto end

:error
echo.
echo ========================================
echo ✗ 打包失败！
echo ========================================
echo 请检查错误信息并重试

:end
echo.
echo 按任意键退出...
pause >nul
