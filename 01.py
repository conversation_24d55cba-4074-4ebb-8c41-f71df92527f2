#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频原创化处理工具
帮助创作者快速处理视频，规避抄袭风险
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import sys
import threading
from pathlib import Path
import subprocess
import tempfile
import shutil
from datetime import datetime
import random
import json


class VideoOriginalityTool:
    def __init__(self, root):
        self.root = root
        self.root.title("视频原创化处理工具")
        self.root.geometry("900x700")

        # 检查FFmpeg
        if not self.check_ffmpeg_installation():
            messagebox.showwarning(
                "依赖检查",
                "未检测到FFmpeg，请确保已正确安装FFmpeg并添加到系统PATH中。\n\n"
                "安装方法：\n"
                "Windows: 下载FFmpeg并添加到PATH\n"
                "macOS: brew install ffmpeg\n"
                "Linux: sudo apt-get install ffmpeg"
            )

        self.setup_ui()
        self.processing = False
    
    def add_placeholder(self, entry, placeholder_text):
        """为Entry添加placeholder文字"""
        entry.insert(0, placeholder_text)
        entry.config(foreground='gray')
        
        def on_focus_in(event):
            if entry.get() == placeholder_text:
                entry.delete(0, tk.END)
                entry.config(foreground='black')
        
        def on_focus_out(event):
            if entry.get() == '':
                entry.insert(0, placeholder_text)
                entry.config(foreground='gray')
        
        entry.bind('<FocusIn>', on_focus_in)
        entry.bind('<FocusOut>', on_focus_out)
    
    def find_chinese_font(self):
        """查找系统中可用的中文字体"""
        font_candidates = [
            ("SimHei.ttf", "黑体"),
            ("msyh.ttc", "微软雅黑"),
            ("msyhbd.ttc", "微软雅黑粗体"),
            ("simsun.ttc", "宋体"),
            ("simkai.ttf", "楷体"),
            ("simfang.ttf", "仿宋"),
            ("STXINWEI.TTF", "华文新魏"),
            ("STKAITI.TTF", "华文楷体"),
            ("STFANGSO.TTF", "华文仿宋"),
            ("STSONG.TTF", "华文宋体"),
            ("STZHONGS.TTF", "华文中宋"),
        ]
        
        font_dirs = [
            "C:/Windows/Fonts/",
            "C:/WINDOWS/Fonts/",
            os.path.join(os.environ.get('WINDIR', 'C:/Windows'), 'Fonts'),
        ]
        
        for font_dir in font_dirs:
            if os.path.exists(font_dir):
                for font_file, font_name in font_candidates:
                    font_path = os.path.join(font_dir, font_file)
                    if os.path.exists(font_path):
                        return font_path, font_name
                    # 尝试大写和小写
                    font_path = os.path.join(font_dir, font_file.lower())
                    if os.path.exists(font_path):
                        return font_path, font_name
                    font_path = os.path.join(font_dir, font_file.upper())
                    if os.path.exists(font_path):
                        return font_path, font_name
        
        return None, None

    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 文件选择区域
        file_frame = ttk.LabelFrame(main_frame, text="文件选择", padding="10")
        file_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Label(file_frame, text="源视频:").grid(row=0, column=0, sticky=tk.W)
        self.source_var = tk.StringVar()
        ttk.Entry(file_frame, textvariable=self.source_var, width=50).grid(row=0, column=1, padx=(5, 5))
        ttk.Button(file_frame, text="浏览", command=self.select_source_file).grid(row=0, column=2)

        ttk.Label(file_frame, text="输出目录:").grid(row=1, column=0, sticky=tk.W)
        self.output_var = tk.StringVar()
        ttk.Entry(file_frame, textvariable=self.output_var, width=50).grid(row=1, column=1, padx=(5, 5))
        ttk.Button(file_frame, text="浏览", command=self.select_output_dir).grid(row=1, column=2)

        # 处理选项区域
        options_frame = ttk.LabelFrame(main_frame, text="原创化处理选项", padding="10")
        options_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        # 视频处理选项
        self.mirror_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="镜像翻转", variable=self.mirror_var).grid(row=0, column=0, sticky=tk.W)

        self.crop_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="智能裁剪", variable=self.crop_var).grid(row=0, column=1, sticky=tk.W)

        self.speed_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="变速处理", variable=self.speed_var).grid(row=0, column=2, sticky=tk.W)

        self.filter_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="滤镜效果", variable=self.filter_var).grid(row=1, column=0, sticky=tk.W)

        self.watermark_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="添加水印", variable=self.watermark_var).grid(row=1, column=1, sticky=tk.W)

        self.audio_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="音频处理", variable=self.audio_var).grid(row=1, column=2, sticky=tk.W)

        # 新增高级处理选项
        self.shake_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="画面抖动", variable=self.shake_var).grid(row=2, column=0, sticky=tk.W)

        self.noise_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="添加噪点", variable=self.noise_var).grid(row=2, column=1, sticky=tk.W)

        self.border_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="随机边框", variable=self.border_var).grid(row=2, column=2, sticky=tk.W)

        self.rotate_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="微旋转", variable=self.rotate_var).grid(row=3, column=0, sticky=tk.W)

        self.trim_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="自动剪切", variable=self.trim_var).grid(row=3, column=1, sticky=tk.W)

        self.dynamic_watermark_var = tk.BooleanVar(value=False)
        ttk.Checkbutton(options_frame, text="动态水印", variable=self.dynamic_watermark_var).grid(row=3, column=2, sticky=tk.W)

        # 高级选项
        advanced_frame = ttk.LabelFrame(main_frame, text="高级选项", padding="10")
        advanced_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Label(advanced_frame, text="裁剪比例:").grid(row=0, column=0, sticky=tk.W)
        self.crop_ratio = tk.StringVar(value="16:9")
        crop_combo = ttk.Combobox(advanced_frame, textvariable=self.crop_ratio,
                                  values=["16:9", "9:16", "1:1", "4:3"], width=10)
        crop_combo.grid(row=0, column=1, padx=(5, 20))

        ttk.Label(advanced_frame, text="变速倍率:").grid(row=0, column=2, sticky=tk.W)
        self.speed_ratio = tk.StringVar(value="1.1")
        speed_combo = ttk.Combobox(advanced_frame, textvariable=self.speed_ratio,
                                   values=["0.9", "1.1", "1.2", "0.8", "随机"], width=10)
        speed_combo.grid(row=0, column=3, padx=(5, 20))

        ttk.Label(advanced_frame, text="处理强度:").grid(row=1, column=0, sticky=tk.W)
        self.intensity_var = tk.StringVar(value="中")
        intensity_combo = ttk.Combobox(advanced_frame, textvariable=self.intensity_var,
                                      values=["低", "中", "高"], width=10)
        intensity_combo.grid(row=1, column=1, padx=(5, 20))

        ttk.Label(advanced_frame, text="水印文字(可选):").grid(row=1, column=2, sticky=tk.W)
        self.watermark_text_var = tk.StringVar(value="")
        watermark_entry = ttk.Entry(advanced_frame, textvariable=self.watermark_text_var, width=20)
        watermark_entry.grid(row=1, column=3, padx=(5, 0))
        # 添加提示文字
        self.add_placeholder(watermark_entry, "留空则不添加水印")

        # 控制按钮
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=3, column=0, columnspan=2, pady=(10, 0))

        self.process_btn = ttk.Button(control_frame, text="开始处理",
                                      command=self.start_processing)
        self.process_btn.pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(control_frame, text="预览效果",
                   command=self.preview_effect).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(control_frame, text="批量处理",
                   command=self.batch_process).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(control_frame, text="保存配置",
                   command=self.save_config).pack(side=tk.LEFT, padx=(0, 10))

        ttk.Button(control_frame, text="加载配置",
                   command=self.load_config).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(control_frame, text="测试简单处理",
                   command=self.test_simple_processing).pack(side=tk.LEFT)

        # 进度显示
        progress_frame = ttk.LabelFrame(main_frame, text="处理进度", padding="10")
        progress_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))

        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var,
                                            maximum=100, length=400)
        self.progress_bar.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 5))

        self.status_var = tk.StringVar(value="就绪")
        ttk.Label(progress_frame, textvariable=self.status_var).grid(row=1, column=0, sticky=tk.W)

        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="处理日志", padding="10")
        log_frame.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))

        self.log_text = tk.Text(log_frame, height=8, width=80)
        scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)

        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(5, weight=1)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)

    def select_source_file(self):
        """选择源视频文件"""
        filename = filedialog.askopenfilename(
            title="选择视频文件",
            filetypes=[
                ("视频文件", "*.mp4 *.avi *.mov *.mkv *.flv *.wmv"),
                ("所有文件", "*.*")
            ]
        )
        if filename:
            self.source_var.set(filename)

    def select_output_dir(self):
        """选择输出目录"""
        dirname = filedialog.askdirectory(title="选择输出目录")
        if dirname:
            self.output_var.set(dirname)

    def log_message(self, message):
        """添加日志消息 - 处理编码问题"""
        try:
            timestamp = datetime.now().strftime("%H:%M:%S")
            # 确保消息是字符串并处理可能的编码问题
            if isinstance(message, bytes):
                message = message.decode('utf-8', errors='ignore')

            log_entry = f"[{timestamp}] {message}\n"
            self.log_text.insert(tk.END, log_entry)
            self.log_text.see(tk.END)
            self.root.update()
        except Exception as e:
            # 如果日志记录失败，至少更新状态
            print(f"日志记录错误: {e}")

    def check_ffmpeg_installation(self):
        """检查FFmpeg是否已安装"""
        try:
            result = subprocess.run(
                ['ffmpeg', '-version'],
                capture_output=True,
                text=True,
                encoding='utf-8',
                errors='ignore',
                timeout=5
            )
            return result.returncode == 0
        except:
            return False

    def safe_file_path(self, file_path):
        """安全处理文件路径，避免编码问题"""
        try:
            # 转换为Path对象处理
            path = Path(file_path)

            # 检查路径是否包含非ASCII字符
            try:
                path.as_posix().encode('ascii')
                return str(path)
            except UnicodeEncodeError:
                # 如果包含非ASCII字符，创建临时文件
                temp_dir = tempfile.gettempdir()
                temp_name = f"temp_video_{datetime.now().strftime('%Y%m%d_%H%M%S')}{path.suffix}"
                temp_path = os.path.join(temp_dir, temp_name)

                # 复制文件到临时位置
                shutil.copy2(file_path, temp_path)
                self.log_message(f"文件路径包含特殊字符，已复制到临时位置: {temp_path}")
                return temp_path

        except Exception as e:
            self.log_message(f"路径处理错误: {e}")
            return file_path

    def start_processing(self):
        """开始处理视频 - 增加路径检查"""
        if self.processing:
            return

        source_file = self.source_var.get()
        output_dir = self.output_var.get()

        if not source_file or not output_dir:
            messagebox.showerror("错误", "请选择源文件和输出目录")
            return

        if not os.path.exists(source_file):
            messagebox.showerror("错误", "源文件不存在")
            return

        # 检查FFmpeg
        if not self.check_ffmpeg_installation():
            messagebox.showerror("错误", "FFmpeg未安装或未添加到PATH中")
            return
            
        # 如果启用了所有选项，建议先测试简单模式
        enabled_count = sum([
            self.mirror_var.get(), self.crop_var.get(), self.speed_var.get(),
            self.filter_var.get(), self.watermark_var.get(), self.audio_var.get(),
            self.shake_var.get(), self.noise_var.get(), self.border_var.get(),
            self.rotate_var.get(), self.trim_var.get()
        ])
        
        if enabled_count > 8:
            if messagebox.askyesno("提示", 
                "您启用了较多处理选项，这可能导致处理失败。\n\n"
                "建议先使用较少的选项测试。\n\n"
                "是否继续？"):
                pass
            else:
                return

        self.processing = True
        self.process_btn.config(state='disabled')

        # 处理文件路径
        safe_source = self.safe_file_path(source_file)

        # 在新线程中处理
        thread = threading.Thread(target=self.process_video,
                                  args=(safe_source, output_dir))
        thread.daemon = True
        thread.start()

    def process_video(self, source_file, output_dir):
        """处理视频的主要逻辑"""
        try:
            self.log_message("开始处理视频...")
            self.status_var.set("正在处理...")

            # 生成输出文件名
            base_name = Path(source_file).stem
            output_file = os.path.join(output_dir, f"{base_name}_原创化.mp4")

            # 构建FFmpeg命令
            cmd = self.build_ffmpeg_command(source_file, output_file)

            # 显示完整命令用于调试
            cmd_str = ' '.join(cmd)
            self.log_message(f"执行命令: {cmd_str}")
            
            # 将完整命令也写入临时文件，方便调试
            debug_file = os.path.join(tempfile.gettempdir(), 'ffmpeg_debug_command.txt')
            with open(debug_file, 'w', encoding='utf-8') as f:
                f.write(cmd_str)
            self.log_message(f"命令已保存到: {debug_file}")

            # 执行处理 - 修复编码问题
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,  # 将stderr重定向到stdout
                text=True,
                encoding='utf-8',  # 明确指定UTF-8编码
                errors='ignore'  # 忽略编码错误
            )

            # 监控进度
            total_frames = self.get_video_frame_count(source_file)
            current_frame = 0
            all_output = []

            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break
                if output:
                    all_output.append(output)
                    # 实时显示输出，特别是错误信息
                    if 'error' in output.lower() or 'invalid' in output.lower():
                        self.log_message(f"FFmpeg: {output.strip()}")
                    # 解析FFmpeg输出获取进度
                    frame_num = self.parse_ffmpeg_progress(output, total_frames)
                    if frame_num > current_frame:
                        current_frame = frame_num

            # 获取剩余输出
            remaining_output = process.stdout.read()
            if remaining_output:
                all_output.append(remaining_output)

            if process.returncode == 0:
                self.log_message(f"处理完成: {output_file}")
                self.status_var.set("处理完成")
                messagebox.showinfo("成功", f"视频处理完成!\n输出文件: {output_file}")
            else:
                # 合并所有输出
                error_output = ''.join(all_output)
                # 显示完整错误信息，不截断
                self.log_message(f"处理失败:\n{error_output}")
                self.status_var.set("处理失败")
                # 在消息框中显示简要错误信息
                error_lines = error_output.strip().split('\n')
                # 查找包含"error"的行
                error_summary = None
                for line in reversed(error_lines):
                    if 'error' in line.lower():
                        error_summary = line
                        break
                if not error_summary:
                    error_summary = error_lines[-1] if error_lines else "未知错误"
                messagebox.showerror("错误", f"视频处理失败！\n\n错误信息：{error_summary}\n\n详细信息请查看日志")

        except UnicodeDecodeError as e:
            self.log_message(f"编码错误: {str(e)}")
            self.status_var.set("编码错误")
            messagebox.showerror("编码错误", "处理过程中遇到编码问题，请检查文件路径是否包含特殊字符")
        except Exception as e:
            self.log_message(f"错误: {str(e)}")
            self.status_var.set("处理失败")
            messagebox.showerror("错误", f"处理过程中出现错误: {str(e)}")
        finally:
            self.processing = False
            self.process_btn.config(state='normal')
            self.progress_var.set(0)
            
            # 清理临时文件
            if hasattr(self, '_temp_files'):
                for temp_file in self._temp_files:
                    try:
                        if os.path.exists(temp_file):
                            os.remove(temp_file)
                            self.log_message(f"已清理临时文件: {temp_file}")
                    except Exception as e:
                        self.log_message(f"清理临时文件失败 {temp_file}: {e}")
                self._temp_files = []

    def get_video_frame_count(self, video_path):
        """获取视频总帧数"""
        try:
            cmd = [
                'ffprobe', '-v', 'quiet', '-select_streams', 'v:0',
                '-count_frames', '-show_entries', 'stream=nb_frames',
                '-csv=p=0', video_path
            ]
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                encoding='utf-8',
                errors='ignore'
            )
            if result.returncode == 0 and result.stdout.strip().isdigit():
                return int(result.stdout.strip())
        except:
            pass
        return 1000  # 默认值
    
    def get_video_duration(self, video_path):
        """获取视频时长（秒）"""
        try:
            cmd = [
                'ffprobe', '-v', 'quiet', '-show_entries',
                'format=duration', '-of', 'default=noprint_wrappers=1:nokey=1',
                video_path
            ]
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                encoding='utf-8',
                errors='ignore'
            )
            if result.returncode == 0:
                return float(result.stdout.strip())
        except:
            pass
        return 0
    
    def get_video_dimensions(self, video_path):
        """获取视频尺寸（宽, 高）"""
        try:
            cmd = [
                'ffprobe', '-v', 'quiet', '-select_streams', 'v:0',
                '-show_entries', 'stream=width,height',
                '-of', 'csv=p=0', video_path
            ]
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                encoding='utf-8',
                errors='ignore'
            )
            if result.returncode == 0 and result.stdout.strip():
                width, height = result.stdout.strip().split(',')
                return int(width), int(height)
        except Exception as e:
            self.log_message(f"获取视频尺寸失败: {e}")
        return None, None

    def parse_ffmpeg_progress(self, output, total_frames=1000):
        """解析FFmpeg输出获取进度"""
        try:
            # 查找frame信息
            if 'frame=' in output:
                parts = output.split()
                for i, part in enumerate(parts):
                    if part.startswith('frame='):
                        frame_str = part.split('=')[1]
                        if frame_str.isdigit():
                            current_frame = int(frame_str)
                            progress = min((current_frame / total_frames) * 100, 95)
                            self.progress_var.set(progress)
                            return current_frame

            # 查找time信息作为备选
            if 'time=' in output:
                self.progress_var.set(min(self.progress_var.get() + 2, 95))

        except Exception as e:
            # 静默处理解析错误
            pass
        return 0

    def get_random_parameters(self):
        """获取随机处理参数"""
        intensity = self.intensity_var.get()
        
        # 根据强度设置参数范围
        if intensity == "低":
            brightness_range = (-0.05, 0.05)
            contrast_range = (0.95, 1.05)
            saturation_range = (0.95, 1.05)
            shake_range = (1, 2)
            noise_range = (5, 10)
            rotate_range = (-0.5, 0.5)
        elif intensity == "高":
            brightness_range = (-0.15, 0.15)
            contrast_range = (0.85, 1.15)
            saturation_range = (0.85, 1.15)
            shake_range = (3, 5)
            noise_range = (15, 25)
            rotate_range = (-2, 2)
        else:  # 中
            brightness_range = (-0.1, 0.1)
            contrast_range = (0.9, 1.1)
            saturation_range = (0.9, 1.1)
            shake_range = (2, 3)
            noise_range = (10, 15)
            rotate_range = (-1, 1)
        
        return {
            'brightness': random.uniform(*brightness_range),
            'contrast': random.uniform(*contrast_range),
            'saturation': random.uniform(*saturation_range),
            'shake': random.randint(*shake_range),
            'noise': random.randint(*noise_range),
            'rotate': random.uniform(*rotate_range)
        }

    def build_ffmpeg_command(self, input_file, output_file):
        """构建FFmpeg处理命令"""
        # 确保路径使用正斜杠，避免Windows路径问题
        input_file = input_file.replace('\\', '/')
        output_file = output_file.replace('\\', '/')
        
        # 注意：subprocess会自动处理包含空格的路径，不需要手动添加引号

        cmd = ['ffmpeg', '-i', input_file]
        
        # 获取随机参数
        random_params = self.get_random_parameters()
        
        # 获取视频尺寸
        video_width, video_height = self.get_video_dimensions(input_file)
        if not video_width or not video_height:
            self.log_message("警告: 无法获取视频尺寸，使用默认参数")
            video_width, video_height = 1920, 1080
        else:
            self.log_message(f"视频尺寸: {video_width}x{video_height}")

        # 视频滤镜列表
        filters = []
        
        # 自动剪切开头结尾
        if self.trim_var.get():
            # 获取视频时长
            duration = self.get_video_duration(input_file)
            if duration > 10:
                start_trim = round(random.uniform(0.5, 2.0), 2)  # 保留2位小数
                end_trim = round(random.uniform(0.5, 2.0), 2)
                new_duration = round(duration - start_trim - end_trim, 2)
                if new_duration > 0:
                    cmd.extend(['-ss', str(start_trim), '-t', str(new_duration)])

        # 镜像翻转
        if self.mirror_var.get():
            filters.append('hflip')
        
        # 微旋转
        if self.rotate_var.get():
            angle = round(random_params['rotate'], 4)  # 保留4位小数
            filters.append(f'rotate={angle}*PI/180:fillcolor=black@0')

        # 智能裁剪
        if self.crop_var.get():
            ratio = self.crop_ratio.get()
            if ratio == "16:9":
                # 计算16:9的目标尺寸
                target_width = video_height * 16 / 9
                if target_width <= video_width:
                    # 如果目标宽度不超过原始宽度，基于高度裁剪
                    crop_w, crop_h = int(target_width), video_height
                    filters.append(f'crop={crop_w}:{crop_h}')
                    self.log_message(f"裁剪至 {ratio}: {crop_w}x{crop_h}")
                else:
                    # 否则基于宽度裁剪
                    target_height = video_width * 9 / 16
                    crop_w, crop_h = video_width, int(target_height)
                    filters.append(f'crop={crop_w}:{crop_h}')
                    self.log_message(f"裁剪至 {ratio}: {crop_w}x{crop_h}")
            elif ratio == "9:16":
                # 计算9:16的目标尺寸
                target_height = video_width * 16 / 9
                if target_height <= video_height:
                    # 如果目标高度不超过原始高度，基于宽度裁剪
                    filters.append(f'crop={video_width}:{int(target_height)}')
                else:
                    # 否则基于高度裁剪
                    target_width = video_height * 9 / 16
                    filters.append(f'crop={int(target_width)}:{video_height}')
            elif ratio == "1:1":
                # 正方形裁剪，取宽高中较小的值
                min_size = min(video_width, video_height)
                filters.append(f'crop={min_size}:{min_size}')
            elif ratio == "4:3":
                # 计算4:3的目标尺寸
                target_width = video_height * 4 / 3
                if target_width <= video_width:
                    filters.append(f'crop={int(target_width)}:{video_height}')
                else:
                    target_height = video_width * 3 / 4
                    filters.append(f'crop={video_width}:{int(target_height)}')
        
        # 画面抖动
        if self.shake_var.get():
            shake_amount = random_params['shake']
            # 确保抖动量不会太大，最多为最小尺寸的0.5%
            max_shake = max(2, int(min(video_width, video_height) * 0.005))
            shake_amount = min(shake_amount, max_shake)
            # 使用随机数种子确保每帧都有不同的抖动
            if shake_amount > 0:
                # 使用简单的表达式，避免复杂的条件判断
                crop_width = f'in_w-{shake_amount*2}'
                crop_height = f'in_h-{shake_amount*2}'
                x_expr = f'random(t)*{shake_amount}*2'
                y_expr = f'random(t+1)*{shake_amount}*2'
                filters.append(f'crop={crop_width}:{crop_height}:{x_expr}:{y_expr}')

        # 滤镜效果
        if self.filter_var.get():
            # 添加随机色彩调整，限制精度
            brightness = round(random_params["brightness"], 3)
            contrast = round(random_params["contrast"], 3)
            saturation = round(random_params["saturation"], 3)
            filters.append(f'eq=brightness={brightness}:contrast={contrast}:saturation={saturation}')
        
        # 添加噪点
        if self.noise_var.get():
            noise_amount = random_params['noise']
            filters.append(f'noise=alls={noise_amount}:allf=t+u')
        
        # 随机边框
        if self.border_var.get():
            border_width = random.randint(5, 20)
            border_color = random.choice(['black', 'white', 'darkgray'])
            filters.append(f'pad=iw+{border_width*2}:ih+{border_width*2}:{border_width}:{border_width}:{border_color}')

        # 添加水印
        if self.watermark_var.get():
            watermark_text = self.watermark_text_var.get()

            # 如果水印文字为空或是placeholder文字，则不添加水印
            if not watermark_text or not watermark_text.strip() or watermark_text == "留空则不添加水印":
                self.log_message("水印文字为空，跳过添加水印")
            else:
                self.log_message(f"添加水印: {watermark_text}")

                if self.dynamic_watermark_var.get():
                    # 动态水印 - 为了避免编码问题，对中文水印使用简化处理
                    if any(ord(c) > 127 for c in watermark_text):
                        self.log_message(f"检测到中文水印，转换为英文避免编码问题: {watermark_text}")
                        # 将中文水印转换为英文时间戳水印
                        watermark_text = f"Original_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                        self.log_message(f"已转换为: {watermark_text}")

                    # 统一使用text参数处理所有水印
                    escaped_text = watermark_text.replace('\\', '\\\\').replace(':', '\\:').replace("'", "\\'")
                    filters.append(f"drawtext=text='{escaped_text}':fontsize=24:fontcolor=white@0.5:x='w-tw-10-20*sin(t)':y='h-th-10-10*cos(t)'")
                else:
                    # 静态水印 - 为了避免编码问题，对中文水印使用简化处理
                    if any(ord(c) > 127 for c in watermark_text):
                        self.log_message(f"检测到中文水印，转换为英文避免编码问题: {watermark_text}")
                        # 将中文水印转换为英文时间戳水印
                        watermark_text = f"Original_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                        self.log_message(f"已转换为: {watermark_text}")

                    # 统一使用text参数处理所有水印
                    escaped_text = watermark_text.replace('\\', '\\\\').replace(':', '\\:').replace("'", "\\'")
                    filters.append(f"drawtext=text='{escaped_text}':fontsize=24:fontcolor=white@0.5:x=w-tw-10:y=h-th-10")

        # 音频滤镜列表
        audio_filters = []
        
        # 变速处理
        if self.speed_var.get():
            speed_ratio = self.speed_ratio.get()
            if speed_ratio == "随机":
                speed = round(random.uniform(0.9, 1.2), 3)
            else:
                speed = float(speed_ratio)
            
            # 添加变速到滤镜链中
            filters.append(f'setpts={1/speed}*PTS')
            
            # 音频变速
            if self.audio_var.get():
                if 0.5 <= speed <= 2.0:
                    audio_filters.append(f'atempo={speed}')
                else:
                    # 处理超出 atempo 范围的情况
                    if speed < 0.5:
                        audio_filters.append(f'atempo=0.5,atempo={speed/0.5}')
                    else:
                        audio_filters.append(f'atempo=2.0,atempo={speed/2.0}')
        
        # 音频处理
        if self.audio_var.get():
            # 音量调整
            volume_change = round(random.uniform(0.95, 1.1), 3)
            audio_filters.append(f'volume={volume_change}')
            
            # 音调变化
            pitch_change = round(random.uniform(0.95, 1.05), 3)
            audio_filters.append(f'asetrate=44100*{pitch_change},aresample=44100')
            
            # 高通滤波
            audio_filters.append('highpass=f=50')
        
        # 应用视频滤镜
        if filters:
            cmd.extend(['-vf', ','.join(filters)])
        
        # 应用音频滤镜
        if audio_filters:
            cmd.extend(['-af', ','.join(audio_filters)])

        # 输出设置
        cmd.extend(['-c:v', 'libx264', '-crf', '23', '-preset', 'medium'])
        cmd.extend(['-c:a', 'aac', '-b:a', '128k'])
        cmd.extend(['-y', output_file])  # -y 覆盖输出文件

        return cmd

    def preview_effect(self):
        """预览处理效果"""
        source_file = self.source_var.get()
        if not source_file:
            messagebox.showerror("错误", "请先选择源文件")
            return

        self.log_message("生成预览中...")
        self.status_var.set("生成预览...")
        
        # 在新线程中生成预览
        thread = threading.Thread(target=self.generate_preview, args=(source_file,))
        thread.daemon = True
        thread.start()
    
    def generate_preview(self, source_file):
        """生成视频预览"""
        try:
            # 创建临时输出文件
            temp_dir = tempfile.gettempdir()
            preview_file = os.path.join(temp_dir, f"preview_{datetime.now().strftime('%Y%m%d_%H%M%S')}.mp4")
            
            # 处理文件路径
            safe_source = self.safe_file_path(source_file)
            
            # 构建预览命令（只处理10秒）
            cmd = self.build_ffmpeg_command(safe_source, preview_file)
            
            # 修改命令以只处理前10秒
            cmd.insert(cmd.index('-i') + 2, '-t')
            cmd.insert(cmd.index('-t') + 1, '10')
            
            self.log_message("正在生成预览...")
            
            process = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                encoding='utf-8',
                errors='ignore'
            )
            
            if process.returncode == 0:
                self.log_message(f"预览生成完成: {preview_file}")
                self.status_var.set("预览就绪")
                
                # 使用默认播放器打开
                if os.name == 'nt':  # Windows
                    os.startfile(preview_file)
                else:  # macOS/Linux
                    subprocess.run(['open' if sys.platform == 'darwin' else 'xdg-open', preview_file])
            else:
                self.log_message("预览生成失败")
                self.status_var.set("预览失败")
                messagebox.showerror("错误", "预览生成失败")
                
        except Exception as e:
            self.log_message(f"预览错误: {e}")
            self.status_var.set("预览失败")
            messagebox.showerror("错误", f"预览生成失败: {e}")

    def batch_process(self):
        """批量处理"""
        files = filedialog.askopenfilenames(
            title="选择多个视频文件",
            filetypes=[
                ("视频文件", "*.mp4 *.avi *.mov *.mkv *.flv *.wmv"),
                ("所有文件", "*.*")
            ]
        )

        if not files:
            return

        output_dir = filedialog.askdirectory(title="选择输出目录")
        if not output_dir:
            return

        self.log_message(f"开始批量处理 {len(files)} 个文件...")

        # 在新线程中批量处理
        thread = threading.Thread(target=self.batch_process_files,
                                  args=(files, output_dir))
        thread.daemon = True
        thread.start()

    def batch_process_files(self, files, output_dir):
        """批量处理文件"""
        self.processing = True
        self.process_btn.config(state='disabled')

        try:
            total_files = len(files)
            for i, file_path in enumerate(files):
                self.log_message(f"处理文件 {i + 1}/{total_files}: {Path(file_path).name}")
                self.status_var.set(f"批量处理中 ({i + 1}/{total_files})")

                # 处理单个文件
                safe_source = self.safe_file_path(file_path)
                base_name = Path(safe_source).stem
                output_file = os.path.join(output_dir, f"{base_name}_原创化.mp4")

                cmd = self.build_ffmpeg_command(safe_source, output_file)

                process = subprocess.run(
                    cmd,
                    capture_output=True,
                    text=True,
                    encoding='utf-8',
                    errors='ignore'
                )

                if process.returncode == 0:
                    self.log_message(f"✅ 完成: {Path(output_file).name}")
                else:
                    self.log_message(f"❌ 失败: {Path(file_path).name}")

                # 更新总进度
                progress = ((i + 1) / total_files) * 100
                self.progress_var.set(progress)

            self.log_message(f"批量处理完成！共处理 {total_files} 个文件")
            self.status_var.set("批量处理完成")
            messagebox.showinfo("完成", f"批量处理完成！\n共处理 {total_files} 个文件")

        except Exception as e:
            self.log_message(f"批量处理错误: {str(e)}")
            messagebox.showerror("错误", f"批量处理失败: {str(e)}")
        finally:
            self.processing = False
            self.process_btn.config(state='normal')
            self.progress_var.set(0)
            
            # 清理临时文件
            if hasattr(self, '_temp_files'):
                for temp_file in self._temp_files:
                    try:
                        if os.path.exists(temp_file):
                            os.remove(temp_file)
                    except Exception:
                        pass
                self._temp_files = []
    
    def save_config(self):
        """保存当前配置"""
        config = {
            'mirror': self.mirror_var.get(),
            'crop': self.crop_var.get(),
            'speed': self.speed_var.get(),
            'filter': self.filter_var.get(),
            'watermark': self.watermark_var.get(),
            'audio': self.audio_var.get(),
            'shake': self.shake_var.get(),
            'noise': self.noise_var.get(),
            'border': self.border_var.get(),
            'rotate': self.rotate_var.get(),
            'trim': self.trim_var.get(),
            'dynamic_watermark': self.dynamic_watermark_var.get(),
            'crop_ratio': self.crop_ratio.get(),
            'speed_ratio': self.speed_ratio.get(),
            'intensity': self.intensity_var.get(),
            'watermark_text': '' if self.watermark_text_var.get() == "留空则不添加水印" else self.watermark_text_var.get()
        }
        
        filename = filedialog.asksaveasfilename(
            title="保存配置",
            defaultextension=".json",
            filetypes=[("配置文件", "*.json"), ("所有文件", "*.*")]
        )
        
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(config, f, indent=4, ensure_ascii=False)
                self.log_message(f"配置已保存: {filename}")
                messagebox.showinfo("成功", "配置保存成功")
            except Exception as e:
                self.log_message(f"保存配置失败: {e}")
                messagebox.showerror("错误", f"保存配置失败: {e}")
    
    def load_config(self):
        """加载配置"""
        filename = filedialog.askopenfilename(
            title="加载配置",
            filetypes=[("配置文件", "*.json"), ("所有文件", "*.*")]
        )
        
        if filename:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                # 应用配置
                self.mirror_var.set(config.get('mirror', True))
                self.crop_var.set(config.get('crop', True))
                self.speed_var.set(config.get('speed', True))
                self.filter_var.set(config.get('filter', True))
                self.watermark_var.set(config.get('watermark', True))
                self.audio_var.set(config.get('audio', True))
                self.shake_var.set(config.get('shake', True))
                self.noise_var.set(config.get('noise', True))
                self.border_var.set(config.get('border', True))
                self.rotate_var.set(config.get('rotate', True))
                self.trim_var.set(config.get('trim', True))
                self.dynamic_watermark_var.set(config.get('dynamic_watermark', False))
                self.crop_ratio.set(config.get('crop_ratio', "16:9"))
                self.speed_ratio.set(config.get('speed_ratio', "1.1"))
                self.intensity_var.set(config.get('intensity', "中"))
                # 处理水印文字，如果为空则显示placeholder
                watermark_text = config.get('watermark_text', "")
                if watermark_text:
                    self.watermark_text_var.set(watermark_text)
                # 如果水印文字为空，placeholder会自动显示
                
                self.log_message(f"配置已加载: {filename}")
                messagebox.showinfo("成功", "配置加载成功")
            except Exception as e:
                self.log_message(f"加载配置失败: {e}")
                messagebox.showerror("错误", f"加载配置失败: {e}")
    
    def test_simple_processing(self):
        """测试简单处理 - 只应用基本功能"""
        source_file = self.source_var.get()
        if not source_file:
            messagebox.showerror("错误", "请先选择源文件")
            return
        
        # 保存当前设置
        saved_settings = {
            'mirror': self.mirror_var.get(),
            'crop': self.crop_var.get(),
            'speed': self.speed_var.get(),
            'filter': self.filter_var.get(),
            'watermark': self.watermark_var.get(),
            'audio': self.audio_var.get(),
            'shake': self.shake_var.get(),
            'noise': self.noise_var.get(),
            'border': self.border_var.get(),
            'rotate': self.rotate_var.get(),
            'trim': self.trim_var.get(),
            'dynamic_watermark': self.dynamic_watermark_var.get()
        }
        
        # 禁用所有选项
        for var in saved_settings:
            getattr(self, f"{var}_var").set(False)
        
        # 只启用镜像翻转
        self.mirror_var.set(True)
        self.log_message("===== 测试模式：只应用镜像翻转 =====")
        
        # 开始处理
        self.start_processing()
        
        # 恢复设置
        def restore_settings():
            for var, value in saved_settings.items():
                getattr(self, f"{var}_var").set(value)
            self.log_message("===== 测试完成，已恢复原设置 =====")
        
        # 5秒后恢复设置
        self.root.after(5000, restore_settings)


def main():
    """主函数"""
    root = tk.Tk()
    app = VideoOriginalityTool(root)
    root.mainloop()


if __name__ == "__main__":
    main()